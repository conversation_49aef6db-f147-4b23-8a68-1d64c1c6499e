#!/usr/bin/env node

/**
 * Google Workspace MCP Server for MxSTER
 * Provides Gmail, Google Drive, Google Calendar, and Google Sheets access
 * Uses service account authentication
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';

class GoogleWorkspaceMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'google-workspace-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    
    // Initialize Google APIs
    this.initializeGoogleAPIs();
  }

  async initializeGoogleAPIs() {
    try {
      const credentialsPath = path.join(process.cwd(), '..', 'credentials', 'google-workspace-service-account.json');
      
      if (!fs.existsSync(credentialsPath)) {
        console.error('Service account credentials not found at:', credentialsPath);
        return;
      }

      const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
      
      this.auth = new google.auth.GoogleAuth({
        credentials,
        scopes: [
          'https://www.googleapis.com/auth/gmail.readonly',
          'https://www.googleapis.com/auth/gmail.send',
          'https://www.googleapis.com/auth/gmail.modify',
          'https://www.googleapis.com/auth/drive',
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/spreadsheets'
        ],
      });

      this.gmail = google.gmail({ version: 'v1', auth: this.auth });
      this.drive = google.drive({ version: 'v3', auth: this.auth });
      this.calendar = google.calendar({ version: 'v3', auth: this.auth });
      this.sheets = google.sheets({ version: 'v4', auth: this.auth });

      console.error('Google Workspace APIs initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Google APIs:', error.message);
    }
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'gmail_search',
          description: 'Search Gmail messages',
          inputSchema: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Gmail search query (e.g., "from:<EMAIL>", "subject:important")'
              },
              maxResults: {
                type: 'number',
                description: 'Maximum number of results to return (default: 10)',
                default: 10
              }
            },
            required: ['query']
          }
        },
        {
          name: 'gmail_read',
          description: 'Read a specific Gmail message',
          inputSchema: {
            type: 'object',
            properties: {
              messageId: {
                type: 'string',
                description: 'Gmail message ID'
              }
            },
            required: ['messageId']
          }
        },
        {
          name: 'gmail_send',
          description: 'Send an email via Gmail',
          inputSchema: {
            type: 'object',
            properties: {
              to: {
                type: 'string',
                description: 'Recipient email address'
              },
              subject: {
                type: 'string',
                description: 'Email subject'
              },
              body: {
                type: 'string',
                description: 'Email body content'
              },
              cc: {
                type: 'string',
                description: 'CC recipients (optional)'
              },
              bcc: {
                type: 'string',
                description: 'BCC recipients (optional)'
              }
            },
            required: ['to', 'subject', 'body']
          }
        },
        {
          name: 'drive_list',
          description: 'List files in Google Drive',
          inputSchema: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'Drive search query (optional)'
              },
              maxResults: {
                type: 'number',
                description: 'Maximum number of results (default: 10)',
                default: 10
              }
            }
          }
        },
        {
          name: 'drive_read',
          description: 'Read content from a Google Drive file',
          inputSchema: {
            type: 'object',
            properties: {
              fileId: {
                type: 'string',
                description: 'Google Drive file ID'
              }
            },
            required: ['fileId']
          }
        },
        {
          name: 'calendar_events',
          description: 'List upcoming calendar events',
          inputSchema: {
            type: 'object',
            properties: {
              maxResults: {
                type: 'number',
                description: 'Maximum number of events (default: 10)',
                default: 10
              },
              timeMin: {
                type: 'string',
                description: 'Start time (ISO format, optional)'
              },
              timeMax: {
                type: 'string',
                description: 'End time (ISO format, optional)'
              }
            }
          }
        },
        {
          name: 'calendar_create',
          description: 'Create a new calendar event',
          inputSchema: {
            type: 'object',
            properties: {
              summary: {
                type: 'string',
                description: 'Event title'
              },
              description: {
                type: 'string',
                description: 'Event description (optional)'
              },
              startTime: {
                type: 'string',
                description: 'Start time (ISO format)'
              },
              endTime: {
                type: 'string',
                description: 'End time (ISO format)'
              },
              attendees: {
                type: 'array',
                items: { type: 'string' },
                description: 'List of attendee email addresses (optional)'
              }
            },
            required: ['summary', 'startTime', 'endTime']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'gmail_search':
            return await this.searchGmail(args);
          case 'gmail_read':
            return await this.readGmail(args);
          case 'gmail_send':
            return await this.sendGmail(args);
          case 'drive_list':
            return await this.listDrive(args);
          case 'drive_read':
            return await this.readDrive(args);
          case 'calendar_events':
            return await this.listCalendarEvents(args);
          case 'calendar_create':
            return await this.createCalendarEvent(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`
            }
          ]
        };
      }
    });
  }

  async searchGmail(args) {
    if (!this.gmail) {
      throw new Error('Gmail API not initialized');
    }

    const response = await this.gmail.users.messages.list({
      userId: 'me',
      q: args.query,
      maxResults: args.maxResults || 10
    });

    const messages = response.data.messages || [];
    const results = [];

    for (const message of messages.slice(0, 5)) { // Limit to 5 for performance
      const details = await this.gmail.users.messages.get({
        userId: 'me',
        id: message.id,
        format: 'metadata',
        metadataHeaders: ['From', 'Subject', 'Date']
      });

      const headers = details.data.payload.headers;
      const from = headers.find(h => h.name === 'From')?.value || 'Unknown';
      const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
      const date = headers.find(h => h.name === 'Date')?.value || 'Unknown';

      results.push({
        id: message.id,
        from,
        subject,
        date,
        snippet: details.data.snippet
      });
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ messages: results, total: messages.length }, null, 2)
        }
      ]
    };
  }

  async readGmail(args) {
    if (!this.gmail) {
      throw new Error('Gmail API not initialized');
    }

    const response = await this.gmail.users.messages.get({
      userId: 'me',
      id: args.messageId,
      format: 'full'
    });

    const message = response.data;
    const headers = message.payload.headers;
    
    const from = headers.find(h => h.name === 'From')?.value || 'Unknown';
    const to = headers.find(h => h.name === 'To')?.value || 'Unknown';
    const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
    const date = headers.find(h => h.name === 'Date')?.value || 'Unknown';

    // Extract body
    let body = '';
    if (message.payload.body?.data) {
      body = Buffer.from(message.payload.body.data, 'base64').toString();
    } else if (message.payload.parts) {
      for (const part of message.payload.parts) {
        if (part.mimeType === 'text/plain' && part.body?.data) {
          body = Buffer.from(part.body.data, 'base64').toString();
          break;
        }
      }
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            id: message.id,
            from,
            to,
            subject,
            date,
            body: body.substring(0, 2000) // Limit body length
          }, null, 2)
        }
      ]
    };
  }

  async sendGmail(args) {
    if (!this.gmail) {
      throw new Error('Gmail API not initialized');
    }

    const email = [
      `To: ${args.to}`,
      args.cc ? `Cc: ${args.cc}` : '',
      args.bcc ? `Bcc: ${args.bcc}` : '',
      `Subject: ${args.subject}`,
      '',
      args.body
    ].filter(line => line !== '').join('\n');

    const encodedEmail = Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_');

    const response = await this.gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedEmail
      }
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ success: true, messageId: response.data.id }, null, 2)
        }
      ]
    };
  }

  async listDrive(args) {
    if (!this.drive) {
      throw new Error('Drive API not initialized');
    }

    const response = await this.drive.files.list({
      q: args.query,
      pageSize: args.maxResults || 10,
      fields: 'files(id,name,mimeType,modifiedTime,size)'
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ files: response.data.files }, null, 2)
        }
      ]
    };
  }

  async readDrive(args) {
    if (!this.drive) {
      throw new Error('Drive API not initialized');
    }

    const response = await this.drive.files.get({
      fileId: args.fileId,
      alt: 'media'
    });

    return {
      content: [
        {
          type: 'text',
          text: response.data.toString().substring(0, 5000) // Limit content
        }
      ]
    };
  }

  async listCalendarEvents(args) {
    if (!this.calendar) {
      throw new Error('Calendar API not initialized');
    }

    const response = await this.calendar.events.list({
      calendarId: 'primary',
      timeMin: args.timeMin || new Date().toISOString(),
      timeMax: args.timeMax,
      maxResults: args.maxResults || 10,
      singleEvents: true,
      orderBy: 'startTime'
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ events: response.data.items }, null, 2)
        }
      ]
    };
  }

  async createCalendarEvent(args) {
    if (!this.calendar) {
      throw new Error('Calendar API not initialized');
    }

    const event = {
      summary: args.summary,
      description: args.description,
      start: {
        dateTime: args.startTime,
        timeZone: 'America/New_York'
      },
      end: {
        dateTime: args.endTime,
        timeZone: 'America/New_York'
      }
    };

    if (args.attendees) {
      event.attendees = args.attendees.map(email => ({ email }));
    }

    const response = await this.calendar.events.insert({
      calendarId: 'primary',
      requestBody: event
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ success: true, eventId: response.data.id, link: response.data.htmlLink }, null, 2)
        }
      ]
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Google Workspace MCP Server running on stdio');
  }
}

const server = new GoogleWorkspaceMCPServer();
server.run().catch(console.error);
