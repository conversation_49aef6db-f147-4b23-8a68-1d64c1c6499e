# Google Workspace Integration for MxSTER

## Overview
MxSTER has seamless access to Google Workspace services through a custom MCP server that uses service account authentication. This enables natural interaction with Gmail, Google Drive, Google Calendar, and Google Sheets without requiring OAuth flows.

## Service Account Details
- **Service Account**: `Google Workspace MCP Server`
- **Email**: `<EMAIL>`
- **Project**: `gen-lang-client-**********` (Gemini API)
- **Credentials File**: `/Users/<USER>/LISSSS/eliza/MISTERELIZA/credentials/google-workspace-service-account.json`

## Available Services

### 📧 Gmail Integration

#### Natural Trigger Phrases:
- "Check my Gmail"
- "Read my emails"
- "Search for emails from [person]"
- "Find emails about [topic]"
- "Send an email to [person]"
- "Email [person] about [topic]"
- "Compose an email"
- "Reply to that email"

#### Available Tools:

**gmail_search**
- **Purpose**: Search Gmail messages
- **Parameters**: 
  - `query` (required): Gmail search query
  - `maxResults` (optional): Number of results (default: 10)
- **Query Examples**:
  - `"from:<EMAIL>"` - Emails from specific sender
  - `"subject:important"` - Emails with "important" in subject
  - `"is:unread"` - Unread emails
  - `"has:attachment"` - Emails with attachments
  - `"after:2025/01/01"` - Emails after specific date

**gmail_read**
- **Purpose**: Read full content of a specific email
- **Parameters**: 
  - `messageId` (required): Message ID from search results

**gmail_send**
- **Purpose**: Send an email
- **Parameters**: 
  - `to` (required): Recipient email
  - `subject` (required): Email subject
  - `body` (required): Email content
  - `cc` (optional): CC recipients
  - `bcc` (optional): BCC recipients

### 📁 Google Drive Integration

#### Natural Trigger Phrases:
- "Check my Drive"
- "Find file [filename]"
- "Search for documents about [topic]"
- "Show me my Drive files"
- "Read that document"
- "Open [filename]"

#### Available Tools:

**drive_list**
- **Purpose**: List files in Google Drive
- **Parameters**: 
  - `query` (optional): Drive search query
  - `maxResults` (optional): Number of results (default: 10)
- **Query Examples**:
  - `"name contains 'report'"` - Files with "report" in name
  - `"mimeType='application/pdf'"` - PDF files only
  - `"modifiedTime > '2025-01-01T00:00:00'"` - Recently modified files

**drive_read**
- **Purpose**: Read content from a Google Drive file
- **Parameters**: 
  - `fileId` (required): Google Drive file ID from list results

### 📅 Google Calendar Integration

#### Natural Trigger Phrases:
- "Check my calendar"
- "What's on my schedule today?"
- "Upcoming meetings"
- "Schedule a meeting with [person]"
- "Create a calendar event"
- "Book an appointment"
- "Add to my calendar"

#### Available Tools:

**calendar_events**
- **Purpose**: List upcoming calendar events
- **Parameters**: 
  - `maxResults` (optional): Number of events (default: 10)
  - `timeMin` (optional): Start time filter (ISO format)
  - `timeMax` (optional): End time filter (ISO format)

**calendar_create**
- **Purpose**: Create a new calendar event
- **Parameters**: 
  - `summary` (required): Event title
  - `description` (optional): Event description
  - `startTime` (required): Start time (ISO format)
  - `endTime` (required): End time (ISO format)
  - `attendees` (optional): Array of attendee email addresses

## Usage Examples

### Gmail Examples:

**Search for recent emails:**
```json
{
  "tool": "gmail_search",
  "parameters": {
    "query": "is:unread after:2025/01/10",
    "maxResults": 5
  }
}
```

**Send a professional email:**
```json
{
  "tool": "gmail_send",
  "parameters": {
    "to": "<EMAIL>",
    "subject": "Project Update",
    "body": "Hi there,\n\nJust wanted to update you on the project status...\n\nBest regards,\nMISTER"
  }
}
```

### Drive Examples:

**Find recent documents:**
```json
{
  "tool": "drive_list",
  "parameters": {
    "query": "modifiedTime > '2025-01-01T00:00:00'",
    "maxResults": 10
  }
}
```

### Calendar Examples:

**Check today's schedule:**
```json
{
  "tool": "calendar_events",
  "parameters": {
    "timeMin": "2025-01-15T00:00:00Z",
    "timeMax": "2025-01-15T23:59:59Z",
    "maxResults": 10
  }
}
```

**Schedule a meeting:**
```json
{
  "tool": "calendar_create",
  "parameters": {
    "summary": "Team Standup",
    "description": "Daily team sync meeting",
    "startTime": "2025-01-16T10:00:00Z",
    "endTime": "2025-01-16T10:30:00Z",
    "attendees": ["<EMAIL>"]
  }
}
```

## Response Behavior

### Natural Acknowledgments
When users request Google Workspace actions, MxSTER should provide natural acknowledgments:
- "Let me check your Gmail..."
- "Searching your Drive for that file..."
- "Looking at your calendar..."
- "Sending that email now..."
- "Creating that calendar event..."

### Error Handling
If Google Workspace tools fail, MxSTER should:
1. Acknowledge the issue naturally
2. Suggest alternative approaches
3. Offer to try again or use different parameters

### Privacy & Security
- Service account has read/write access to Gmail, Drive, and Calendar
- All interactions are logged for debugging
- Sensitive information should be handled appropriately
- Users should be informed about what actions are being taken

## Integration Priority
Google Workspace tools should be used FIRST when users make requests that match the trigger phrases. MxSTER should seamlessly integrate these capabilities into his natural conversation flow without exposing technical details about MCP servers or API calls.

## Limitations
- Service account authentication (no OAuth required)
- Limited to the configured Google account
- File reading is limited to text-based content
- Large files may be truncated for performance
- Calendar events default to America/New_York timezone

## Troubleshooting
If Google Workspace integration fails:
1. Check service account credentials file exists
2. Verify Google APIs are enabled in the project
3. Ensure proper permissions are granted
4. Check network connectivity
5. Review error logs for specific issues
