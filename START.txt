cd MISTERELIZA && elizaos start --character ./characters/MiSTERv2.character.json
pnpm start --characters=./characters/MxSTER.character.json


        },
        "google-workspace": {
          "type": "stdio",
          "name": "Google Workspace Integration",
          "command": "npx",
          "args": [
            "-y",
            "@modelcontextprotocol/server-google-workspace"
          ],
          "env": {
            "GOOGLE_APPLICATION_CREDENTIALS": "${GOOGLE_APPLICATION_CREDENTIALS}",
            "GOOGLE_CLIENT_ID": "${GOOGLE_CLIENT_ID}",
            "GOOGLE_CLIENT_SECRET": "${GOOGLE_CLIENT_SECRET}",
            "GOOGLE_REFRESH_TOKEN": "${GOOGLE_REFRESH_TOKEN}",
            "GMAIL_USER_EMAIL": "${GMAIL_USER_EMAIL}"
          }
